import path from 'node:path'
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import VueRouter from "unplugin-vue-router/vite";
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  base: "./",
  plugins: [
    VueRouter({
      extensions: [".vue"],
      routesFolder: "src/pages",
      exclude: ['src/pages/**/components/**','src/pages/**/common/**'],//排除components和common文件夹
    }),
    vue(),
    Components(),
  ],
  server: {
    host: true,
    port: 8000,
    proxy: {
      "/api": {
        target: "http://************:18127",
        ws: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  resolve: {
    alias: {
      "@": path.join(__dirname, "./src"),
      "~": path.join(__dirname, "./src/assets"),
      "~root": path.join(__dirname, "."),
    },
  },
});
