# 房联（funi）移动端前端项目

基于 Vue 3 + Vite + Vant 的移动端前端项目，专为房联业务场景设计。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 7.0.3
- **UI 组件库**: Vant 4.9.20 (移动端 UI 库)
- **路由**: Vue Router 4.5.1 + unplugin-vue-router (文件系统路由)
- **状态管理**: Pinia 3.0.3 + pinia-plugin-persistedstate (持久化)
- **样式**: SCSS + PostCSS (移动端适配)
- **HTTP 请求**: @funi-lib/utils (房联自研工具库)
- **包管理**: pnpm

## 快速开始

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

开发服务器将在 http://localhost:8000 启动。

## 项目结构

```
src/
├── api/                    # API 接口定义
├── assets/                 # 静态资源
├── components/             # 公共组件
├── pages/                  # 页面组件 (文件系统路由)
├── router/                 # 路由配置
├── stores/                 # Pinia 状态管理
├── utils/                  # 工具函数
├── App.vue                # 根组件
├── main.js                # 应用入口
└── style.css              # 全局样式
```

## 文档

### 开发文档
- [AI 开发指南](docs/AI-DEVELOPMENT-GUIDE.md) - 完整的开发规范和最佳实践
- [快速参考指南](docs/QUICK-REFERENCE.md) - 常用代码片段和配置速查
- [项目配置说明](docs/PROJECT-CONFIG.md) - 详细的配置文件说明

### AI 提示词
- [AI 提示词模板](docs/proompts/AI-PROMPT-TEMPLATE.md) - 标准化的 AI 开发提示词

## 开发规范

### 页面开发
- 使用 Vue 3 Composition API
- 页面文件放在 `src/pages/` 目录，支持文件系统路由
- 必须包含 `<route>` 块定义路由信息
- 样式使用 SCSS，必须添加 `scoped`

### 组件开发
- 优先使用 Vant UI 组件库
- 自定义组件放在 `src/components/` 目录
- 使用 PascalCase 命名组件文件

### 状态管理
- 使用 Pinia 进行状态管理
- 支持持久化存储
- 按功能模块拆分 Store

### HTTP 请求
- 使用全局 `window.$http` 实例
- 自动处理认证和加密
- 统一错误处理

## 移动端适配

- 设计稿基准：375px
- 最大显示宽度：600px
- 自动 px 转换和响应式适配
- 支持安全区域适配

## 浏览器支持

- iOS Safari 12+
- Android Chrome 70+
- 微信内置浏览器
- 支付宝内置浏览器

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目为房联内部项目，仅供内部使用。