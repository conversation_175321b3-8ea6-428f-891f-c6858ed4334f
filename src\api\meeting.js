/**
 * 会议相关 API
 */
export const meetingApi = {
  /**
   * 获取待开会议列表-移动端
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} params.assignee - 当前用户账号
   * @param {string} [params.conferenceName] - 会议名称（可选）
   * @returns {Promise} 返回会议列表数据
   *
   * 返回数据结构：
   * {
   *   code: 0,
   *   success: true,
   *   data: {
   *     records: [
   *       {
   *         conferenceName: "会议名称",
   *         startTime: "开始时间",
   *         confereAddress: "会议地址",
   *         fromId: "表单ID",
   *         createTime: "创建时间",
   *         meetingAgendaListVos: [
   *           {
   *             id: 1,
   *             topicName: "议题名称",
   *             topicContent: "议题内容",
   *             agendaOrder: 1
   *           }
   *         ]
   *       }
   *     ],
   *     total: 总数,
   *     current: 当前页,
   *     size: 每页条数,
   *     pages: 总页数
   *   },
   *   msg: "返回消息"
   * }
   */
  getKeeperFromDataApp: (params) => {
    return window.$http.fetch('/conference/keeperFromDataApp', params)
  },

  /**
   * 获取会议详情
   * @param {Object} params - 查询参数
   * @param {string} params.id - 会议ID (fromId)
   * @returns {Promise} 返回会议详情数据
   */
  getMeetingDetail: (params) => {
    return window.$http.fetch(`/conference/detail/${params.id}`)
  }
}
