/**
 * 会议相关 API
 */
export const meetingApi = {
  /**
   * 获取待开会议列表-移动端
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} params.assignee - 当前用户账号
   * @param {string} [params.conferenceName] - 会议名称（可选）
   * @returns {Promise} 返回会议列表数据
   *
   * 返回数据结构：
   * {
   *   code: 0,
   *   success: true,
   *   data: {
   *     records: [
   *       {
   *         conferenceName: "会议名称",
   *         startTime: "开始时间",
   *         confereAddress: "会议地址",
   *         fromId: "表单ID",
   *         createTime: "创建时间",
   *         meetingAgendaListVos: [
   *           {
   *             id: 1,
   *             topicName: "议题名称",
   *             topicContent: "议题内容",
   *             agendaOrder: 1
   *           }
   *         ]
   *       }
   *     ],
   *     total: 总数,
   *     current: 当前页,
   *     size: 每页条数,
   *     pages: 总页数
   *   },
   *   msg: "返回消息"
   * }
   */
  getKeeperFromDataApp: (params) => {
    return window.$http.fetch('/conference/keeperFromDataApp', params)
  },

  /**
   * 获取会议详情-移动端
   * @param {Object} params - 查询参数
   * @param {string} params.fromId - 会议ID (fromId)
   * @returns {Promise} 返回会议详情数据
   *
   * 接口地址: GET /conference/meetingDetilApp
   *
   * 返回数据结构：
   * {
   *   code: 0,
   *   success: true,
   *   data: {
   *     conferenceName: "会议名称",
   *     conferenceLocation: "会议地点",
   *     participants: ["与会人员"],
   *     participantsId: ["与会人员id"],
   *     startTime: "开始时间",
   *     sponsorDepartment: "主办部门",
   *     conferenceProperty: "会议性质",
   *     host: ["主持人"],
   *     importanceLevel: "重要级别 0-普通 1-重要",
   *     hostId: ["主持人ID"],
   *     fromId: "表单ID",
   *     deptIds: ["部门ID"],
   *     updateButtonStatus: 0,
   *     id: 0,
   *     meetingAgendaList: [
   *       {
   *         id: 0,
   *         fromId: 0,
   *         topicName: "议题名称",
   *         topicContent: "议题内容",
   *         attend: "列席",
   *         viewPersonId: 0,
   *         viewPersonName: "查看人员名称",
   *         createTime: "创建时间",
   *         updateTime: "更新时间",
   *         agendaOrder: 0,
   *         isDelete: 0,
   *         deleteStatus: "删除状态",
   *         urlList: ["附件URL"],
   *         issuePictureList: [
   *           {
   *             id: 0,
   *             meetingAgendaId: 0,
   *             meetingUrl: "议题文件",
   *             meetingName: "文件名称",
   *             createTime: "创建时间",
   *             uodateTime: "更新时间",
   *             isDelete: 0
   *           }
   *         ]
   *       }
   *     ]
   *   },
   *   msg: "返回消息"
   * }
   */
  getMeetingDetail: (params) => {
    return window.$http.fetch('/conference/meetingDetilApp', { fromId: params.fromId })
  },

  /**
   * 议题分页查询
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} params.fromId - 会议ID (fromId)
   * @returns {Promise} 返回议题列表数据
   *
   * 接口地址: GET /conference/meetingListPage
   *
   * 返回数据结构：
   * {
   *   code: 0,
   *   success: true,
   *   data: {
   *     records: [
   *       {
   *         id: 0,
   *         fromId: 0,
   *         topicName: "议题名称",
   *         topicContent: "议题内容",
   *         attend: "列席",
   *         viewPersonId: 0,
   *         viewPersonName: "查看人员名称",
   *         createTime: "创建时间",
   *         updateTime: "更新时间",
   *         agendaOrder: 0,
   *         isDelete: 0,
   *         deleteStatus: "删除状态",
   *         urlList: ["附件URL"],
   *         issuePictureList: [
   *           {
   *             id: 0,
   *             meetingAgendaId: 0,
   *             meetingUrl: "议题文件",
   *             meetingName: "文件名称",
   *             createTime: "创建时间",
   *             uodateTime: "更新时间",
   *             isDelete: 0
   *           }
   *         ]
   *       }
   *     ],
   *     total: 0,
   *     size: 0,
   *     current: 0,
   *     pages: 0
   *   },
   *   msg: "返回消息"
   * }
   */
  getMeetingAgendaList: (params) => {
    return window.$http.fetch('/conference/meetingListPage', params)
  }
}
