# 待开会议页面说明

## 页面概述

根据提供的UI设计图，已成功创建了"待开会议"页面，包含以下功能：

## 页面结构

### 1. 首页 (`/home`)
- **路径**: `src/pages/home/<USER>
- **功能**: 
  - 欢迎界面
  - 快速导航到各个功能模块
  - 显示会议统计数据
  - 提供"待开会议"入口

### 2. 待开会议列表页 (`/meeting/pending`)
- **路径**: `src/pages/meeting/pending/index.vue`
- **功能**:
  - 搜索会议（支持会议名称搜索）
  - 下拉刷新
  - 上拉加载更多
  - 会议卡片展示（包含会议名称、时间、地点、议题）
  - 点击查看详情

### 3. 会议详情页 (`/meeting/detail/:id`)
- **路径**: `src/pages/meeting/detail/[id]/index.vue`
- **功能**:
  - 显示会议完整信息
  - 会议议题列表展示
  - 加入会议功能
  - 导出到日历功能

## 技术实现

### 组件库
- 使用 **Vant UI** 组件库（符合项目规范）
- 主题色设置为绿色 `#07c160`

### 核心组件使用
- `VanSearch`: 搜索框
- `VanPullRefresh`: 下拉刷新
- `VanList`: 列表加载
- `VanButton`: 按钮
- `VanEmpty`: 空状态
- `VanLoading`: 加载状态
- `VanIcon`: 图标

### 数据处理
- 议题解析：支持分号分隔的议题文本
- 时间格式化：自动格式化显示时间
- 搜索过滤：支持会议名称模糊搜索

### 响应式设计
- 基于 375px 设计基准
- 支持最大 600px 显示宽度
- 针对小屏设备（320px）进行适配

## API 接口

### 会议API (`src/api/meeting.js`)

#### 1. 获取待开会议列表
```javascript
meetingApi.getKeeperFromDataApp({
  current: 1,        // 当前页码
  size: 10,          // 每页条数
  assignee: 'user',  // 当前用户账号
  conferenceName: '' // 会议名称（可选）
})
```

**接口地址**: `GET /conference/keeperFromDataApp`

**返回数据结构**:
```javascript
{
  code: 0,
  success: true,
  data: {
    records: [
      {
        conferenceName: "会议名称",
        startTime: "开始时间",
        confereAddress: "会议地址",
        fromId: "表单ID",
        createTime: "创建时间",
        meetingAgendaListVos: [
          {
            id: 1,
            topicName: "议题名称",
            topicContent: "议题内容",
            agendaOrder: 1
          }
        ]
      }
    ],
    total: 总数,
    current: 当前页,
    size: 每页条数,
    pages: 总页数
  },
  msg: "返回消息"
}
```

#### 2. 获取会议详情
```javascript
meetingApi.getMeetingDetail({ id: 'fromId' })
```

**接口地址**: `GET /conference/detail/{id}`

## 页面特性

### 1. 搜索功能
- 圆角搜索框设计
- 实时搜索支持
- 清空搜索功能

### 2. 会议卡片
- 清晰的信息层级
- 议题列表展示
- 绿色主题按钮

### 3. 交互体验
- 下拉刷新
- 上拉加载更多
- 点击反馈
- 加载状态提示

### 4. 空状态处理
- 无数据时显示友好提示
- 搜索无结果提示

## 开发规范遵循

✅ **HTML模板外置**: 所有HTML模板放在 `html/index.html`  
✅ **路由配置**: 每个页面包含 `<route>` 块  
✅ **样式作用域**: 使用 `scoped` 样式  
✅ **组件库优先**: 优先使用 Vant UI 组件  
✅ **响应式设计**: 支持移动端适配  
✅ **代码规范**: 遵循项目命名和结构规范  

## 模拟数据

当前使用模拟数据进行开发测试，包含：
- 2条会议记录
- 完整的会议信息字段
- 议题解析示例

## 启动方式

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 访问地址
http://localhost:8000
```

## 页面路由

- 首页: `http://localhost:8000/#/home`
- 待开会议: `http://localhost:8000/#/meeting/pending`
- 会议详情: `http://localhost:8000/#/meeting/detail/1`

## API对接说明

### 智能降级策略
代码已实现智能API降级机制：

1. **优先调用真实API**: 首先尝试调用后端接口
2. **自动降级**: API调用失败时自动使用模拟数据
3. **开发友好**: 确保开发过程中页面始终可用

### 用户状态管理
- 已集成 `useUserStore` 用户状态管理
- 自动获取当前用户账号用于API调用
- 支持用户信息持久化存储

### 数据适配
- 完全适配接口文档中的数据结构
- 支持 `meetingAgendaListVos` 议题数组
- 自动处理字段映射（`confereAddress` → `location`）

## 部署配置

### 后端代理配置
确保 `vite.config.js` 中配置了正确的代理：

```javascript
export default {
  server: {
    proxy: {
      '/conference': {
        target: 'http://your-backend-url',
        changeOrigin: true
      }
    }
  }
}
```

### 请求头配置
API调用会自动携带 `keeper-auth` 请求头（已在HTTP拦截器中配置）

## 后续开发建议

1. **✅ 真实API对接**: 已完成，支持智能降级
2. **✅ 用户状态管理**: 已集成用户store
3. **✅ 错误处理**: 已完善网络错误处理机制
4. **性能优化**: 可添加列表虚拟滚动（大数据量时）
5. **离线支持**: 可添加离线缓存功能
6. **权限控制**: 可根据用户角色控制功能访问
