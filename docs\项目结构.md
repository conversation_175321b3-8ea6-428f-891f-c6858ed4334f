src/
├── api/                    # API 接口定义
│   └── meeting.js         # 会议相关API
├── assets/                 # 静态资源
│   ├── images/            # 图片资源
│   └── icons/             # 图标资源
├── components/             # 公共组件（自动注册）
│   └── NavBar.vue         # 导航栏组件
├── pages/                  # 页面组件 (文件系统路由)
│   ├── index/             # 首页模块
│   │   ├── index.vue      # 首页
│   │   ├── components/    # 页面私有组件
│   │   ├── common/        # 页面共用逻辑
│   │   └── html/          # 页面 HTML 模板
│   │       └── index.html # 首页HTML模板
│   └── meeting/           # 会议管理模块
│       ├── index.vue      # 会议列表页面
│       ├── components/    # 页面私有组件
│       ├── common/        # 页面共用逻辑
│       └── html/          # 页面 HTML 模板
│           └── index.html # 会议列表HTML模板
├── router/                 # 路由配置
│   └── index.js           # 路由入口
├── stores/                 # Pinia 状态管理
│   ├── index.js           # Store 入口
│   └── modules/           # Store 模块
│       └── app.js         # 应用状态
├── utils/                  # 工具函数
│   └── http/              # HTTP 请求封装
├── style/                  # 全局样式
│   └── theme.css          # Vant 主题变量
├── App.vue                # 根组件
├── main.js                # 应用入口
└── style.css              # 全局样式