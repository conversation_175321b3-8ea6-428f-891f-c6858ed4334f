<!-- 公共导航栏 -->
<script setup>
import { computed } from 'vue'
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

function onBack() {
  if (window.history.state.back)
    history.back()
  else
    router.replace('/')
}


const title = computed(() => {
  if (!route.meta)
    return ''

  return route.meta.title
})

const showLeftArrow = computed(() => {
  // 首页不显示返回按钮
  const hideArrowRoutes = ['Home']
  return !hideArrowRoutes.includes(route.name)
})
</script>

<template>
  <VanNavBar
    :title="title"
    :fixed="true"
    clickable placeholder
    :left-arrow="showLeftArrow"
    @click-left="onBack"
  />
</template>
