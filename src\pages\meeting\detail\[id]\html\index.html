<div class="meeting-detail-page">
  <VanLoading v-if="loading" class="loading-container" />

  <div v-else-if="meetingDetail" class="detail-content">
    <!-- 会议标题 -->
    <div class="title-section">
      <h1 class="meeting-title">{{ meetingDetail.conferenceName }}</h1>
    </div>

    <!-- 会议基本信息 -->
    <div class="info-section">
      <div class="info-row">
        <span class="info-label">开始时间</span>
        <span class="info-value">{{ meetingDetail.startTime }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">会议地点</span>
        <span class="info-value">{{ meetingDetail.location }}</span>
      </div>

      <div class="info-row">
        <span class="info-label">主持人</span>
        <span class="info-value">{{ meetingDetail.host || '张三' }}</span>
      </div>

      <div class="info-row participants-row">
        <span class="info-label">与会人员</span>
        <div class="participants-list">
          <span
            v-for="(participant, index) in meetingDetail.participantsList"
            :key="index"
            class="participant-tag"
          >
            {{ participant }}
          </span>
        </div>
      </div>
    </div>

    <!-- 会议议题信息 -->
    <div class="agenda-section">
      <div class="section-header">
        <h3 class="section-title">会议议题信息</h3>
        <div class="header-actions">
          <VanButton
            size="small"
            plain 
            icon="upgrade"
            @click="exportAgenda"
          >
            议题导出
          </VanButton>
        </div>
      </div>

      <div class="agenda-list">
        <div
          v-for="(agendaItem, index) in meetingDetail.agendaList"
          :key="agendaItem.id || index"
          class="agenda-item"
        >
          <div class="agenda-header">
            <div class="agenda-number">{{ index + 1 }}</div>
            <div class="agenda-title">{{ agendaItem.title }}</div>
          </div>

          <!-- 附件列表 -->
          <div v-if="agendaItem.attachments && agendaItem.attachments.length > 0" class="attachments">
            <div class="attachments-label">附件：</div>
            <div class="attachment-list">
              <div
                v-for="attachment in agendaItem.attachments"
                :key="attachment.id"
                class="attachment-item"
              >
                <VanIcon name="description" class="attachment-icon" />
                <span class="attachment-name">{{ attachment.name }}</span>
                <div class="attachment-actions">
                  <VanButton
                    size="mini"
                    @click="previewAttachment(attachment)"
                  >
                    预览
                  </VanButton>
                  <VanButton
                    size="mini"
                    @click="downloadAttachment(attachment)"
                  >
                    下载
                  </VanButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 错误状态 -->
  <VanEmpty
    v-else
    description="会议信息不存在"
    image="error"
  />
</div>
