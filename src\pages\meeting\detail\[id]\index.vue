<!-- 会议详情页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { meetingApi } from '@/api/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const meetingDetail = ref(null)

// 方法
const fetchMeetingDetail = async () => {
  loading.value = true

  try {
    const meetingId = route.params.id

    try {
      // 尝试调用真实API
      const response = await meetingApi.getMeetingDetail({ id: meetingId })

      if (response && response.success && response.data) {
        meetingDetail.value = {
          ...response.data,
          id: response.data.fromId,
          agenda: processAgendaList(response.data.meetingAgendaListVos || []),
          startTime: formatDateTime(response.data.startTime),
          location: response.data.confereAddress || '会议地点待定'
        }
        return
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)
    }

    // API调用失败时使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 800))

    const mockDetail = {
      fromId: meetingId,
      conferenceName: '紧急技术评审',
      startTime: '2025-06-30 18:58:51',
      confereAddress: '成都市农业农村局1006会议室',
      createTime: '2025-06-30 16:00:00',
      description: '针对高标准农田建设项目进行技术评审，讨论技术方案的可行性和实施计划。',
      host: '张三',
      participants: '陈宇航、宋一、王若琳、孙语桐、周夏、吴欣妍、徐浩然',
      meetingAgendaListVos: [
        {
          id: 1,
          topicName: '会议开场',
          topicContent: '会议开场介绍',
          agendaOrder: 1,
          attachments: []
        },
        {
          id: 2,
          topicName: '产品需求讨论',
          topicContent: '讨论产品功能需求和用户体验',
          agendaOrder: 2,
          attachments: [
            {
              id: 1,
              name: '产品需求文档.pdf',
              url: '/files/product-requirements.pdf',
              type: 'pdf'
            },
            {
              id: 2,
              name: '用户调研数据用户调研数据.xlsx',
              url: '/files/user-research.xlsx',
              type: 'excel'
            }
          ]
        },
        {
          id: 3,
          topicName: '技术方案评审',
          topicContent: '评审技术实现方案和架构设计',
          agendaOrder: 3,
          attachments: [
            {
              id: 3,
              name: '技术方案设计.docx',
              url: '/files/tech-design.docx',
              type: 'word'
            },
            {
              id: 4,
              name: '系统架构图.png',
              url: '/files/architecture.png',
              type: 'image'
            }
          ]
        },
        {
          id: 4,
          topicName: '时间计划制定',
          topicContent: '制定项目时间计划和里程碑',
          agendaOrder: 4,
          attachments: []
        },
        {
          id: 5,
          topicName: '会议总结',
          topicContent: '总结会议要点和后续行动',
          agendaOrder: 5,
          attachments: []
        }
      ]
    }

    meetingDetail.value = {
      ...mockDetail,
      id: mockDetail.fromId,
      agenda: processAgendaList(mockDetail.meetingAgendaListVos || []),
      agendaList: processDetailedAgendaList(mockDetail.meetingAgendaListVos || []),
      participantsList: processParticipantsList(mockDetail.participants || ''),
      startTime: formatDateTime(mockDetail.startTime),
      location: mockDetail.confereAddress || '会议地点待定'
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    showToast('获取会议详情失败')
  } finally {
    loading.value = false
  }
}

// 处理议题列表（新的数据结构）
const processAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  // 按照 agendaOrder 排序，然后提取议题名称
  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => item.topicName || item.topicContent || '')
    .filter(item => item.trim())
}

// 处理详细议题列表（包含附件信息）
const processDetailedAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => ({
      id: item.id,
      title: item.topicName || item.topicContent || '',
      content: item.topicContent || '',
      order: item.agendaOrder || 0,
      attachments: item.attachments || []
    }))
    .filter(item => item.title.trim())
}

// 处理参会人员列表
const processParticipantsList = (participantsText) => {
  if (!participantsText) return []

  // 按照中文标点符号分割参会人员
  return participantsText
    .split(/[、，,；;]/)
    .map(name => name.trim())
    .filter(name => name)
}

// 解析会议议题（兼容旧格式）
const parseAgenda = (agendaText) => {
  if (!agendaText) return []

  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 预览附件
const previewAttachment = (attachment) => {
  try {
    // 这里可以实现附件预览功能
    console.log('预览附件:', attachment)
    showToast(`预览 ${attachment.name}`)

    // 可以根据文件类型打开不同的预览方式
    if (attachment.type === 'pdf') {
      // PDF预览
      window.open(attachment.url, '_blank')
    } else if (attachment.type === 'image') {
      // 图片预览
      window.open(attachment.url, '_blank')
    } else {
      // 其他类型文件
      showToast('该文件类型暂不支持预览')
    }
  } catch (error) {
    console.error('预览附件失败:', error)
    showToast('预览失败')
  }
}

// 下载附件
const downloadAttachment = (attachment) => {
  try {
    console.log('下载附件:', attachment)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = attachment.url
    link.download = attachment.name
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    showToast(`开始下载 ${attachment.name}`)
  } catch (error) {
    console.error('下载附件失败:', error)
    showToast('下载失败')
  }
}

// 导出议题
const exportAgenda = () => {
  try {
    const meeting = meetingDetail.value

    // 生成议题文本内容
    let agendaText = `会议议题信息\n\n会议名称：${meeting.conferenceName}\n开始时间：${meeting.startTime}\n会议地点：${meeting.location}\n主持人：${meeting.host}\n\n议题列表：\n`

    meeting.agendaList.forEach((agenda, index) => {
      agendaText += `${index + 1}. ${agenda.title}\n`
      if (agenda.attachments && agenda.attachments.length > 0) {
        agendaText += `   附件：\n`
        agenda.attachments.forEach(att => {
          agendaText += `   - ${att.name}\n`
        })
      }
      agendaText += '\n'
    })

    // 创建下载链接
    const blob = new Blob([agendaText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${meeting.conferenceName}-议题信息.txt`
    link.click()

    // 清理URL对象
    URL.revokeObjectURL(url)

    showToast('议题信息已导出')
  } catch (error) {
    console.error('导出议题失败:', error)
    showToast('导出失败')
  }
}

// 生命周期
onMounted(() => {
  fetchMeetingDetail()
})
</script>

<style lang="scss" scoped>
.meeting-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding-top: 46px; // 导航栏高度
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding: var(--van-padding-md);
}

.title-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  text-align: center;

  .meeting-title {
    font-size: 18px;
    font-weight: var(--van-font-bold);
    color: var(--van-text-color);
    margin: 0;
    line-height: var(--van-line-height-lg);
  }
}

.info-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);

  .info-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--van-padding-md);

    &:last-child {
      margin-bottom: 0;
    }

    &.participants-row {
      flex-direction: column;

      .participants-list {
        margin-top: var(--van-padding-xs);
        display: flex;
        flex-wrap: wrap;
        gap: var(--van-padding-xs);

        .participant-tag {
          background: var(--van-gray-2);
          color: var(--van-text-color);
          padding: 2px 8px;
          border-radius: var(--van-radius-sm);
          font-size: var(--van-font-size-sm);
          line-height: 1.4;
        }
      }
    }

    .info-label {
      color: var(--van-text-color-2);
      font-size: var(--van-font-size-md);
      min-width: 80px;
      flex-shrink: 0;
    }

    .info-value {
      color: var(--van-text-color);
      font-size: var(--van-font-size-md);
      flex: 1;
      line-height: var(--van-line-height-md);
    }
  }
}

.agenda-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--van-padding-lg);

    .section-title {
      font-size: var(--van-font-size-lg);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin: 0;
    }
  }

  .agenda-list {
    .agenda-item {
      margin-bottom: var(--van-padding-xl);

      &:last-child {
        margin-bottom: 0;
      }

      .agenda-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--van-padding-md);

        .agenda-number {
          background: var(--van-primary-color);
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--van-font-size-sm);
          font-weight: var(--van-font-bold);
          margin-right: var(--van-padding-sm);
          flex-shrink: 0;
        }

        .agenda-title {
          flex: 1;
          font-size: var(--van-font-size-md);
          font-weight: var(--van-font-bold);
          color: var(--van-text-color);
          line-height: var(--van-line-height-md);
        }
      }

      .attachments {
        margin-left: 32px; // 对齐议题内容

        .attachments-label {
          font-size: var(--van-font-size-sm);
          color: var(--van-text-color-2);
          margin-bottom: var(--van-padding-xs);
        }

        .attachment-list {
          .attachment-item {
            display: flex;
            align-items: center;
            background: var(--van-gray-1);
            border-radius: var(--van-radius-md);
            padding: var(--van-padding-sm);
            margin-bottom: var(--van-padding-xs);

            &:last-child {
              margin-bottom: 0;
            }

            .attachment-icon {
              color: var(--van-text-color-2);
              font-size: 16px;
              margin-right: var(--van-padding-xs);
              flex-shrink: 0;
            }

            .attachment-name {
              flex: 1;
              font-size: var(--van-font-size-sm);
              color: var(--van-text-color);
              line-height: var(--van-line-height-sm);
              margin-right: var(--van-padding-sm);
              word-break: break-all;
            }

            .attachment-actions {
              display: flex;
              gap: var(--van-padding-xs);
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 320px) {
  .detail-content {
    padding: var(--van-padding-sm);
  }

  .title-section,
  .info-section,
  .agenda-section {
    padding: var(--van-padding-md);
  }

  .agenda-section {
    .agenda-list {
      .agenda-item {
        .attachments {
          margin-left: 28px;

          .attachment-list {
            .attachment-item {
              flex-direction: column;
              align-items: flex-start;

              .attachment-name {
                margin-right: 0;
                margin-bottom: var(--van-padding-xs);
              }

              .attachment-actions {
                align-self: flex-end;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingDetail',
  meta: {
    title: '会议详情'
  }
}
</route>
