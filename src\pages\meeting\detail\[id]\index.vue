<!-- 会议详情页面 -->
<template src="./html/index.html"></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { meetingApi } from '@/api/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const meetingDetail = ref(null)

// 方法
const fetchMeetingDetail = async () => {
    loading.value = true
    const meetingId = route.params.id
      // 尝试调用真实API - 使用新的接口
      const data = await meetingApi.getMeetingDetail({ fromId: meetingId })

      if (data) {
        // 适配新的数据结构
        meetingDetail.value = {
          ...data,
          id: data.fromId,
          // 使用新接口返回的字段
          conferenceName: data.conferenceName,
          location: data.conferenceLocation || '会议地点待定',
          startTime: formatDateTime(data.startTime),
          // 处理与会人员
          participants: Array.isArray(data.participants) ? data.participants.join('、') : (data.participants || ''),
          participantsList: Array.isArray(data.participants) ? data.participants : [],
          // 处理主持人
          host: Array.isArray(data.host) ? data.host.join('、') : (data.host || ''),
          // 处理议题列表 - 使用新的字段名 meetingAgendaList
          agenda: processAgendaList(data.meetingAgendaList || []),
          agendaList: processDetailedAgendaList(data.meetingAgendaList || []),
          // 其他字段
          sponsorDepartment: data.sponsorDepartment || '',
          conferenceProperty: data.conferenceProperty || '',
          importanceLevel: data.importanceLevel || '0'
        }
      }
  loading.value = false
}

// 处理议题列表（新的数据结构）
const processAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  // 按照 agendaOrder 排序，然后提取议题名称
  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => item.topicName || item.topicContent || '')
    .filter(item => item.trim())
}

// 处理详细议题列表（包含附件信息）
const processDetailedAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => ({
      id: item.id,
      title: item.topicName || item.topicContent || '',
      content: item.topicContent || '',
      order: item.agendaOrder || 0,
      attend: item.attend || '', // 列席人员
      viewPersonName: item.viewPersonName || '', // 查看人员名称
      // 处理附件信息 - 使用新接口的 issuePictureList 和 urlList
      attachments: processAttachments(item.issuePictureList || [], item.urlList || [])
    }))
    .filter(item => item.title.trim())
}

// 处理附件信息
const processAttachments = (issuePictureList, urlList) => {
  const attachments = []

  // 处理 issuePictureList 中的附件
  if (Array.isArray(issuePictureList)) {
    issuePictureList.forEach(picture => {
      if (picture.meetingUrl && picture.meetingName) {
        attachments.push({
          id: picture.id,
          name: picture.meetingName,
          url: picture.meetingUrl,
          type: getFileType(picture.meetingName),
          createTime: picture.createTime
        })
      }
    })
  }

  // 处理 urlList 中的附件
  if (Array.isArray(urlList)) {
    urlList.forEach((url, index) => {
      if (url) {
        const fileName = getFileNameFromUrl(url)
        attachments.push({
          id: `url_${index}`,
          name: fileName,
          url: url,
          type: getFileType(fileName)
        })
      }
    })
  }

  return attachments
}

// 从URL中提取文件名
const getFileNameFromUrl = (url) => {
  if (!url) return '附件'
  const parts = url.split('/')
  return parts[parts.length - 1] || '附件'
}

// 根据文件名获取文件类型
const getFileType = (fileName) => {
  if (!fileName) return 'unknown'
  const ext = fileName.split('.').pop()?.toLowerCase()

  const typeMap = {
    'pdf': 'pdf',
    'doc': 'word',
    'docx': 'word',
    'xls': 'excel',
    'xlsx': 'excel',
    'ppt': 'powerpoint',
    'pptx': 'powerpoint',
    'jpg': 'image',
    'jpeg': 'image',
    'png': 'image',
    'gif': 'image',
    'txt': 'text',
    'zip': 'archive',
    'rar': 'archive'
  }

  return typeMap[ext] || 'unknown'
}

// 处理参会人员列表
const processParticipantsList = (participantsText) => {
  if (!participantsText) return []

  // 按照中文标点符号分割参会人员
  return participantsText
    .split(/[、，,；;]/)
    .map(name => name.trim())
    .filter(name => name)
}

// 解析会议议题（兼容旧格式）
const parseAgenda = (agendaText) => {
  if (!agendaText) return []

  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''

  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 预览附件
const previewAttachment = (attachment) => {
  try {
    // 这里可以实现附件预览功能
    console.log('预览附件:', attachment)
    showToast(`预览 ${attachment.name}`)

    // 可以根据文件类型打开不同的预览方式
    if (attachment.type === 'pdf') {
      // PDF预览
      window.open(attachment.url, '_blank')
    } else if (attachment.type === 'image') {
      // 图片预览
      window.open(attachment.url, '_blank')
    } else {
      // 其他类型文件
      showToast('该文件类型暂不支持预览')
    }
  } catch (error) {
    console.error('预览附件失败:', error)
    showToast('预览失败')
  }
}

// 下载附件
const downloadAttachment = (attachment) => {
  try {
    console.log('下载附件:', attachment)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = attachment.url
    link.download = attachment.name
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    showToast(`开始下载 ${attachment.name}`)
  } catch (error) {
    console.error('下载附件失败:', error)
    showToast('下载失败')
  }
}

// 查看详细议题列表
const viewAgendaList = () => {
  const meetingId = route.params.id
  router.push({
    name: 'MeetingAgenda',
    params: { id: meetingId }
  })
}

// 导出议题
const exportAgenda = () => {
  try {
    const meeting = meetingDetail.value

    // 生成议题文本内容
    let agendaText = `会议议题信息\n\n会议名称：${meeting.conferenceName}\n开始时间：${meeting.startTime}\n会议地点：${meeting.location}\n主持人：${meeting.host}\n\n议题列表：\n`

    meeting.agendaList.forEach((agenda, index) => {
      agendaText += `${index + 1}. ${agenda.title}\n`
      if (agenda.attachments && agenda.attachments.length > 0) {
        agendaText += `   附件：\n`
        agenda.attachments.forEach(att => {
          agendaText += `   - ${att.name}\n`
        })
      }
      agendaText += '\n'
    })

    // 创建下载链接
    const blob = new Blob([agendaText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${meeting.conferenceName}-议题信息.txt`
    link.click()

    // 清理URL对象
    URL.revokeObjectURL(url)

    showToast('议题信息已导出')
  } catch (error) {
    console.error('导出议题失败:', error)
    showToast('导出失败')
  }
}

// 生命周期
onMounted(() => {
  fetchMeetingDetail()
})
</script>

<style lang="scss" scoped>
.meeting-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding: var(--van-padding-md);
}

.title-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  text-align: center;

  .meeting-title {
    font-size: 18px;
    font-weight: var(--van-font-bold);
    color: var(--van-text-color);
    margin: 0;
    line-height: var(--van-line-height-lg);
  }
}

.info-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);

  .info-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--van-padding-md);

    &:last-child {
      margin-bottom: 0;
    }

    &.participants-row {
      flex-direction: column;

      .participants-list {
        margin-top: var(--van-padding-xs);
        display: flex;
        flex-wrap: wrap;
        gap: var(--van-padding-xs);

        .participant-tag {
          background: var(--van-gray-2);
          color: var(--van-text-color);
          padding: 2px 8px;
          border-radius: var(--van-radius-sm);
          font-size: var(--van-font-size-sm);
          line-height: 1.4;
        }
      }
    }

    .info-label {
      color: var(--van-text-color-2);
      font-size: var(--van-font-size-md);
      min-width: 80px;
      flex-shrink: 0;
    }

    .info-value {
      color: var(--van-text-color);
      font-size: var(--van-font-size-md);
      flex: 1;
      line-height: var(--van-line-height-md);
    }
  }
}

.agenda-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--van-padding-lg);

    .section-title {
      font-size: var(--van-font-size-lg);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: var(--van-padding-xs);
    }
  }

  .agenda-list {
    .agenda-item {
      margin-bottom: var(--van-padding-xl);

      &:last-child {
        margin-bottom: 0;
      }

      .agenda-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--van-padding-md);

        .agenda-number {
          background: var(--van-primary-color);
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--van-font-size-sm);
          font-weight: var(--van-font-bold);
          margin-right: var(--van-padding-sm);
          flex-shrink: 0;
        }

        .agenda-title {
          flex: 1;
          font-size: var(--van-font-size-md);
          font-weight: var(--van-font-bold);
          color: var(--van-text-color);
          line-height: var(--van-line-height-md);
        }
      }

      .attachments {
        margin-left: 32px; // 对齐议题内容

        .attachments-label {
          font-size: var(--van-font-size-sm);
          color: var(--van-text-color-2);
          margin-bottom: var(--van-padding-xs);
        }

        .attachment-list {
          .attachment-item {
            display: flex;
            align-items: center;
            background: var(--van-gray-1);
            border-radius: var(--van-radius-md);
            padding: var(--van-padding-sm);
            margin-bottom: var(--van-padding-xs);

            &:last-child {
              margin-bottom: 0;
            }

            .attachment-icon {
              color: var(--van-text-color-2);
              font-size: 16px;
              margin-right: var(--van-padding-xs);
              flex-shrink: 0;
            }

            .attachment-name {
              flex: 1;
              font-size: var(--van-font-size-sm);
              color: var(--van-text-color);
              line-height: var(--van-line-height-sm);
              margin-right: var(--van-padding-sm);
              word-break: break-all;
            }

            .attachment-actions {
              display: flex;
              gap: var(--van-padding-xs);
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 320px) {
  .detail-content {
    padding: var(--van-padding-sm);
  }

  .title-section,
  .info-section,
  .agenda-section {
    padding: var(--van-padding-md);
  }

  .agenda-section {
    .agenda-list {
      .agenda-item {
        .attachments {
          margin-left: 28px;

          .attachment-list {
            .attachment-item {
              flex-direction: column;
              align-items: flex-start;

              .attachment-name {
                margin-right: 0;
                margin-bottom: var(--van-padding-xs);
              }

              .attachment-actions {
                align-self: flex-end;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<route lang="json5">
{
  name: "MeetingDetail",
  meta: {
    title: "会议详情",
  },
}
</route>
