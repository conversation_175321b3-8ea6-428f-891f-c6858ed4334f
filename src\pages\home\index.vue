<!-- 首页 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { meetingApi } from '@/api/meeting'

const router = useRouter()

// 响应式数据
const stats = ref({
  pending: 0,
  today: 0,
  total: 0
})

// 方法
const fetchStats = async () => {
  try {
    // 这里可以调用统计API获取数据
    // 暂时使用模拟数据
    stats.value = {
      pending: 5,
      today: 3,
      total: 28
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 导航方法
const goToMeetingPending = () => {
  router.push({ name: 'MeetingPending' })
}

const goToMeetingHistory = () => {
  // 这里可以导航到历史会议页面
  console.log('导航到历史会议')
}

const goToMeetingCreate = () => {
  // 这里可以导航到创建会议页面
  console.log('导航到创建会议')
}

const goToMeetingManage = () => {
  // 这里可以导航到会议管理页面
  console.log('导航到会议管理')
}

// 生命周期
onMounted(() => {
  fetchStats()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--van-primary-color) 0%, #4facfe 100%);
  padding-top: 46px; // 导航栏高度
}

.welcome-section {
  text-align: center;
  padding: var(--van-padding-xl) var(--van-padding-md);
  color: white;
  
  .welcome-title {
    font-size: 28px;
    font-weight: var(--van-font-bold);
    margin: 0 0 var(--van-padding-sm) 0;
  }
  
  .welcome-subtitle {
    font-size: var(--van-font-size-md);
    opacity: 0.9;
    margin: 0;
  }
}

.menu-grid {
  padding: 0 var(--van-padding-md);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--van-padding-md);
  margin-bottom: var(--van-padding-xl);
  
  .menu-item {
    background: var(--van-background-2);
    border-radius: var(--van-radius-lg);
    padding: var(--van-padding-lg);
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
    
    .menu-icon {
      color: var(--van-primary-color);
      margin-bottom: var(--van-padding-sm);
    }
    
    .menu-title {
      font-size: var(--van-font-size-lg);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin-bottom: var(--van-padding-xs);
    }
    
    .menu-desc {
      font-size: var(--van-font-size-sm);
      color: var(--van-text-color-2);
      line-height: var(--van-line-height-sm);
    }
  }
}

.quick-stats {
  background: var(--van-background-2);
  margin: 0 var(--van-padding-md);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 24px;
      font-weight: var(--van-font-bold);
      color: var(--van-primary-color);
      margin-bottom: var(--van-padding-xs);
    }
    
    .stat-label {
      font-size: var(--van-font-size-sm);
      color: var(--van-text-color-2);
    }
  }
}

// 响应式适配
@media (max-width: 320px) {
  .welcome-section {
    padding: var(--van-padding-lg) var(--van-padding-sm);
    
    .welcome-title {
      font-size: 24px;
    }
  }
  
  .menu-grid {
    padding: 0 var(--van-padding-sm);
    gap: var(--van-padding-sm);
    
    .menu-item {
      padding: var(--van-padding-md);
    }
  }
  
  .quick-stats {
    margin: 0 var(--van-padding-sm);
    padding: var(--van-padding-md);
  }
}
</style>

<route lang="json5">
{
  name: 'Home',
  meta: {
    title: '房联会议'
  }
}
</route>
