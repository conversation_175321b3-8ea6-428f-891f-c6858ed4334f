<!-- 待开会议页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { meetingApi } from '@/api/meeting'
import { useUserStore } from '@/stores/modules/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const meetingList = ref([])
const currentPage = ref(1)

// 方法
const fetchMeetingList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

    loading.value = true
    const params = {
      current: isRefresh ? 1 : currentPage.value,
      size: 10,
      assignee: '',
      ...(searchKeyword.value && { conferenceName: searchKeyword.value }),
      conferenceName:''
    }

    const response = await meetingApi.getKeeperFromDataApp(params)

    // 检查响应结构
    if (response.records) {
      // 处理会议数据，适配新的数据结构
      const processedMeetings = response.records.map(meeting => ({
        ...meeting,
        id: meeting.fromId, // 使用 fromId 作为会议ID
        agenda: processAgendaList(meeting.meetingAgendaListVos || []),
        startTime: formatDateTime(meeting.startTime),
        location: meeting.confereAddress || '会议地点待定'
      }))

      if (isRefresh) {
        meetingList.value = processedMeetings
        currentPage.value = 1
      } else {
        meetingList.value.push(...processedMeetings)
      }

      // 判断是否还有更多数据
      const totalPages = response.pages || 0
      finished.value = currentPage.value >= totalPages

      if (!isRefresh) {
        currentPage.value++
      }
    }
  loading.value = false
  refreshing.value = false
}


// 处理议题列表（新的数据结构）
const processAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  // 按照 agendaOrder 排序，然后提取议题名称
  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => item.topicName || item.topicContent || '')
    .filter(item => item.trim())
}

// 解析会议议题（兼容旧格式）
const parseAgenda = (agendaText) => {
  if (!agendaText) return []

  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  fetchMeetingList(true)
}

// 加载更多
const onLoad = () => {
  if (!finished.value) {
    fetchMeetingList()
  }
}

// 搜索
const onSearch = () => {
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 查看详情
const viewDetail = (meeting) => {
  router.push({
    name: 'MeetingDetail',
    params: { id: meeting.id }
  })
}

// 生命周期
onMounted(() => {
  fetchMeetingList(true)
})
</script>

<style lang="scss" scoped>
.meeting-pending-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-container {
  background: var(--van-background-2);
  border-bottom: 1px solid var(--van-border-color);
}

.meeting-list {
  padding: 16px;
}

.meeting-item {
  background: var(--van-background-2);
  border-radius: var(--van-radius-md);
  padding: var(--van-padding-md);
  margin-bottom: var(--van-padding-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .meeting-header {
    margin-bottom: var(--van-padding-md);
    
    .meeting-title {
      font-size: var(--van-font-size-md);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin: 0;
      line-height: var(--van-line-height-md);
    }
  }

  .meeting-info {
    margin-bottom: var(--van-padding-md);
    
    .info-row {
      display: flex;
      margin-bottom: var(--van-padding-sm);
      
      &.agenda {
        flex-direction: column;
        background-color: #F2F3F5;
        padding: 8px;
        .label{
          color:#303133;
          font-weight: bold;
        }
        .agenda-content {
          margin-top: var(--van-padding-xs);
          
          p {
            margin: 0 0 var(--van-padding-xs) 0;
            color: var(--van-text-color-2);
            font-size: var(--van-font-size-md);
            line-height: var(--van-line-height-md);
          }
        }
      }
      
      .label {
        color: var(--van-text-color-2);
        font-size: var(--van-font-size-md);
        min-width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: var(--van-text-color);
        font-size: var(--van-font-size-md);
        flex: 1;
      }
    }
  }

  .meeting-actions {
    text-align: center;
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingPending',
  meta: {
    title: '待开会议'
  }
}
</route>
