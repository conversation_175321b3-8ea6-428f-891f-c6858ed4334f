<!-- 会议议题列表页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { meetingApi } from '@/api/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const agendaList = ref([])
const currentPage = ref(1)
const meetingInfo = ref({})

// 方法
const fetchAgendaList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

  loading.value = true
  
  try {
    const meetingId = route.params.id
    const params = {
      current: isRefresh ? 1 : currentPage.value,
      size: 10,
      fromId: meetingId
    }

    const response = await meetingApi.getMeetingAgendaList(params)

    if (response && response.success && response.data) {
      // 处理议题数据
      const processedAgendas = response.data.records.map(agenda => ({
        ...agenda,
        // 处理附件信息
        attachments: processAttachments(agenda.issuePictureList || [], agenda.urlList || [])
      }))

      if (isRefresh) {
        agendaList.value = processedAgendas
        currentPage.value = 1
      } else {
        agendaList.value.push(...processedAgendas)
      }

      // 判断是否还有更多数据
      const totalPages = response.data.pages || 0
      finished.value = currentPage.value >= totalPages

      if (!isRefresh) {
        currentPage.value++
      }
    }
  } catch (error) {
    console.error('获取议题列表失败:', error)
    showToast('获取议题列表失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 获取会议基本信息
const fetchMeetingInfo = async () => {
  try {
    const meetingId = route.params.id
    const response = await meetingApi.getMeetingDetail({ fromId: meetingId })
    
    if (response && response.success && response.data) {
      meetingInfo.value = {
        conferenceName: response.data.conferenceName,
        startTime: formatDateTime(response.data.startTime),
        location: response.data.conferenceLocation || '会议地点待定'
      }
    }
  } catch (error) {
    console.error('获取会议信息失败:', error)
  }
}

// 处理附件信息
const processAttachments = (issuePictureList, urlList) => {
  const attachments = []
  
  // 处理 issuePictureList 中的附件
  if (Array.isArray(issuePictureList)) {
    issuePictureList.forEach(picture => {
      if (picture.meetingUrl && picture.meetingName) {
        attachments.push({
          id: picture.id,
          name: picture.meetingName,
          url: picture.meetingUrl,
          type: getFileType(picture.meetingName),
          createTime: picture.createTime
        })
      }
    })
  }
  
  // 处理 urlList 中的附件
  if (Array.isArray(urlList)) {
    urlList.forEach((url, index) => {
      if (url) {
        const fileName = getFileNameFromUrl(url)
        attachments.push({
          id: `url_${index}`,
          name: fileName,
          url: url,
          type: getFileType(fileName)
        })
      }
    })
  }
  
  return attachments
}

// 从URL中提取文件名
const getFileNameFromUrl = (url) => {
  if (!url) return '附件'
  const parts = url.split('/')
  return parts[parts.length - 1] || '附件'
}

// 根据文件名获取文件类型
const getFileType = (fileName) => {
  if (!fileName) return 'unknown'
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  const typeMap = {
    'pdf': 'pdf',
    'doc': 'word',
    'docx': 'word',
    'xls': 'excel',
    'xlsx': 'excel',
    'ppt': 'powerpoint',
    'pptx': 'powerpoint',
    'jpg': 'image',
    'jpeg': 'image',
    'png': 'image',
    'gif': 'image',
    'txt': 'text',
    'zip': 'archive',
    'rar': 'archive'
  }
  
  return typeMap[ext] || 'unknown'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  fetchAgendaList(true)
}

// 加载更多
const onLoad = () => {
  if (!finished.value) {
    fetchAgendaList()
  }
}

// 获取附件图标
const getAttachmentIcon = (type) => {
  const iconMap = {
    'pdf': 'description',
    'word': 'description',
    'excel': 'description',
    'powerpoint': 'description',
    'image': 'photo-o',
    'text': 'description',
    'archive': 'description',
    'unknown': 'description'
  }

  return iconMap[type] || 'description'
}

// 预览附件
const previewAttachment = (attachment) => {
  if (attachment.url) {
    // 这里可以实现文件预览功能
    window.open(attachment.url, '_blank')
  }
}

// 返回会议详情
const goBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  fetchMeetingInfo()
  fetchAgendaList(true)
})
</script>

<style lang="scss" scoped>
.agenda-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.meeting-header {
  background: var(--van-background-2);
  padding: var(--van-padding-md);
  border-bottom: 1px solid var(--van-border-color);
  
  .meeting-title {
    font-size: var(--van-font-size-lg);
    font-weight: var(--van-font-bold);
    color: var(--van-text-color);
    margin: 0 0 var(--van-padding-sm) 0;
  }
  
  .meeting-meta {
    font-size: var(--van-font-size-sm);
    color: var(--van-text-color-2);
    
    .meta-item {
      margin-bottom: var(--van-padding-xs);
    }
  }
}

.agenda-list {
  padding: 16px;
}

.agenda-item {
  background: var(--van-background-2);
  border-radius: var(--van-radius-md);
  padding: var(--van-padding-md);
  margin-bottom: var(--van-padding-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .agenda-header {
    margin-bottom: var(--van-padding-md);
    
    .agenda-order {
      display: inline-block;
      background: var(--van-primary-color);
      color: white;
      padding: 2px 8px;
      border-radius: var(--van-radius-sm);
      font-size: var(--van-font-size-sm);
      margin-right: var(--van-padding-sm);
    }
    
    .agenda-title {
      font-size: var(--van-font-size-md);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
    }
  }

  .agenda-content {
    margin-bottom: var(--van-padding-md);
    
    .content-text {
      color: var(--van-text-color-2);
      font-size: var(--van-font-size-md);
      line-height: var(--van-line-height-md);
    }
  }

  .agenda-meta {
    margin-bottom: var(--van-padding-md);
    
    .meta-row {
      display: flex;
      margin-bottom: var(--van-padding-xs);
      
      .label {
        color: var(--van-text-color-2);
        font-size: var(--van-font-size-sm);
        min-width: 60px;
        flex-shrink: 0;
      }
      
      .value {
        color: var(--van-text-color);
        font-size: var(--van-font-size-sm);
        flex: 1;
      }
    }
  }

  .attachments {
    .attachments-title {
      font-size: var(--van-font-size-sm);
      color: var(--van-text-color-2);
      margin-bottom: var(--van-padding-sm);
    }
    
    .attachment-item {
      display: flex;
      align-items: center;
      padding: var(--van-padding-sm);
      background: var(--van-background);
      border-radius: var(--van-radius-sm);
      margin-bottom: var(--van-padding-xs);
      
      .attachment-icon {
        margin-right: var(--van-padding-sm);
        color: var(--van-primary-color);
      }
      
      .attachment-name {
        flex: 1;
        font-size: var(--van-font-size-sm);
        color: var(--van-text-color);
        word-break: break-all;
      }
    }
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingAgenda',
  meta: {
    title: '会议议题'
  }
}
</route>
