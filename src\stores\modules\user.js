import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 用户状态管理
 */
export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref('')
  const assignee = ref('current_user') // 当前用户账号，用于API调用

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const currentAssignee = computed(() => assignee.value || 'current_user')

  // 方法
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info && info.username) {
      assignee.value = info.username
    }
  }

  const setToken = (newToken) => {
    token.value = newToken
  }

  const setAssignee = (newAssignee) => {
    assignee.value = newAssignee
  }

  const login = async (credentials) => {
    try {
      // 这里可以调用登录API
      const response = await window.$http.post('/api/login', credentials)
      
      if (response && response.success) {
        setToken(response.token)
        setUserInfo(response.user)
        return response
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const logout = () => {
    userInfo.value = null
    token.value = ''
    assignee.value = 'current_user'
  }

  return {
    userInfo,
    token,
    assignee,
    isLoggedIn,
    currentAssignee,
    setUserInfo,
    setToken,
    setAssignee,
    login,
    logout
  }
}, {
  persist: true // 启用持久化
})
