<div class="agenda-page">
  <!-- 会议信息头部 -->
  <div class="meeting-header">
    <h2 class="meeting-title">{{ meetingInfo.conferenceName || '会议议题' }}</h2>
    <div class="meeting-meta">
      <div class="meta-item" v-if="meetingInfo.startTime">
        <VanIcon name="clock-o" size="14" />
        {{ meetingInfo.startTime }}
      </div>
      <div class="meta-item" v-if="meetingInfo.location">
        <VanIcon name="location-o" size="14" />
        {{ meetingInfo.location }}
      </div>
    </div>
  </div>

  <!-- 议题列表 -->
  <div class="agenda-list">
    <VanPullRefresh v-model="refreshing" @refresh="onRefresh">
      <VanList
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="agenda in agendaList"
          :key="agenda.id"
          class="agenda-item"
        >
          <!-- 议题标题 -->
          <div class="agenda-header">
            <span class="agenda-order">{{ agenda.agendaOrder || '1' }}</span>
            <span class="agenda-title">{{ agenda.topicName || '议题标题' }}</span>
          </div>
          
          <!-- 议题内容 -->
          <div class="agenda-content" v-if="agenda.topicContent">
            <p class="content-text">{{ agenda.topicContent }}</p>
          </div>
          
          <!-- 议题元信息 -->
          <div class="agenda-meta">
            <div class="meta-row" v-if="agenda.attend">
              <span class="label">列席：</span>
              <span class="value">{{ agenda.attend }}</span>
            </div>
            <div class="meta-row" v-if="agenda.viewPersonName">
              <span class="label">查看人：</span>
              <span class="value">{{ agenda.viewPersonName }}</span>
            </div>
            <div class="meta-row" v-if="agenda.createTime">
              <span class="label">创建时间：</span>
              <span class="value">{{ agenda.createTime }}</span>
            </div>
          </div>
          
          <!-- 附件列表 -->
          <div class="attachments" v-if="agenda.attachments && agenda.attachments.length > 0">
            <div class="attachments-title">
              <VanIcon name="paperclip" size="14" />
              附件 ({{ agenda.attachments.length }})
            </div>
            <div
              v-for="attachment in agenda.attachments"
              :key="attachment.id"
              class="attachment-item"
              @click="previewAttachment(attachment)"
            >
              <VanIcon 
                :name="getAttachmentIcon(attachment.type)" 
                size="16" 
                class="attachment-icon"
              />
              <span class="attachment-name">{{ attachment.name }}</span>
              <VanIcon name="arrow" size="12" />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <VanEmpty
          v-if="!loading && agendaList.length === 0"
          description="暂无议题"
          image="search"
        />
      </VanList>
    </VanPullRefresh>
  </div>
</div>
